{% extends 'base.html' %}
{% load static %}

{% block title %}Find Your Perfect Pet | PetPaw{% endblock %}

{% block extra_css %}
<style>
    /* Hero Section */
    .pet-hero {
        background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-light) 100%);
        padding: var(--spacing-4xl) 0 var(--spacing-3xl);
        margin-bottom: var(--spacing-3xl);
        position: relative;
        overflow: hidden;
    }

    .pet-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("{% static 'img/paw-pattern.svg' %}") repeat;
        opacity: 0.05;
        z-index: 1;
    }

    .pet-hero-content {
        position: relative;
        z-index: 2;
        text-align: center;
        max-width: 800px;
        margin: 0 auto;
    }

    .pet-hero-title {
        font-size: var(--font-h1);
        font-weight: var(--fw-bold);
        color: var(--primary);
        margin-bottom: var(--spacing-lg);
        line-height: var(--line-height-h1);
    }

    .pet-hero-description {
        font-size: var(--font-lg);
        color: var(--text-light);
        margin-bottom: var(--spacing-2xl);
        line-height: var(--line-height-loose);
    }

    .pet-hero-stats {
        display: flex;
        justify-content: center;
        gap: var(--spacing-3xl);
        margin-top: var(--spacing-xl);
    }

    .hero-stat {
        text-align: center;
    }

    .hero-stat-number {
        font-size: var(--font-h3);
        font-weight: var(--fw-bold);
        color: var(--primary);
        display: block;
    }

    .hero-stat-label {
        font-size: var(--font-sm);
        color: var(--text-light);
        margin-top: var(--spacing-xs);
    }

    /* Search and Filter Section */
    .search-filter-section {
        margin-bottom: var(--spacing-3xl);
    }

    .search-bar-container {
        background-color: var(--white);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-lg);
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-xl);
        position: relative;
    }

    .search-bar {
        display: flex;
        align-items: center;
        gap: var(--spacing-base);
    }

    .search-input-wrapper {
        flex: 1;
        position: relative;
    }

    .search-input {
        width: 100%;
        padding: var(--spacing-base) var(--spacing-xl) var(--spacing-base) var(--spacing-4xl);
        border: 2px solid var(--gray-200);
        border-radius: var(--radius-full);
        font-size: var(--font-base);
        transition: var(--transition-base);
        background-color: var(--gray-50);
    }

    .search-input:focus {
        outline: none;
        border-color: var(--primary);
        background-color: var(--white);
        box-shadow: 0 0 0 3px var(--primary-light);
    }

    .search-icon {
        position: absolute;
        left: var(--spacing-base);
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-light);
        font-size: var(--font-lg);
    }

    .view-toggle {
        display: flex;
        background-color: var(--gray-100);
        border-radius: var(--radius-full);
        padding: var(--spacing-xs);
    }

    .view-toggle-btn {
        padding: var(--spacing-sm) var(--spacing-base);
        border: none;
        background: transparent;
        border-radius: var(--radius-full);
        cursor: pointer;
        transition: var(--transition-base);
        color: var(--text-light);
    }

    .view-toggle-btn.active {
        background-color: var(--primary);
        color: var(--white);
    }

    /* Enhanced Filters */
    .pet-filters {
        background-color: var(--white);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-lg);
        padding: var(--spacing-2xl);
        margin-bottom: var(--spacing-2xl);
        border: 1px solid var(--gray-100);
    }

    .filters-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-xl);
        padding-bottom: var(--spacing-base);
        border-bottom: 1px solid var(--gray-200);
    }

    .filters-title {
        font-size: var(--font-lg);
        font-weight: var(--fw-semibold);
        color: var(--text);
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .filter-form {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: var(--spacing-xl);
    }

    .filter-group {
        position: relative;
    }

    .filter-group-icon {
        position: absolute;
        top: var(--spacing-2xl);
        left: var(--spacing-base);
        color: var(--primary);
        font-size: var(--font-sm);
        z-index: 2;
    }

    .filter-actions {
        grid-column: 1 / -1;
        display: flex;
        justify-content: center;
        gap: var(--spacing-base);
        margin-top: var(--spacing-lg);
        padding-top: var(--spacing-lg);
        border-top: 1px solid var(--gray-200);
    }

    /* Enhanced Pet Grid */
    .pets-section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-xl);
    }

    .pets-count {
        color: var(--text-light);
        font-size: var(--font-sm);
    }

    .sort-dropdown {
        position: relative;
    }

    .sort-select {
        padding: var(--spacing-sm) var(--spacing-lg);
        border: 1px solid var(--gray-300);
        border-radius: var(--radius-full);
        background-color: var(--white);
        font-size: var(--font-sm);
        cursor: pointer;
        transition: var(--transition-base);
    }

    .sort-select:focus {
        outline: none;
        border-color: var(--primary);
        box-shadow: 0 0 0 3px var(--primary-light);
    }

    .pet-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: var(--spacing-2xl);
        margin-bottom: var(--spacing-3xl);
    }

    /* Enhanced Pet Cards */
    .pet-card {
        background-color: var(--white);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-base);
        overflow: hidden;
        transition: var(--transition-slow);
        border: 1px solid var(--gray-100);
        position: relative;
        cursor: pointer;
    }

    .pet-card:hover {
        transform: translateY(-8px);
        box-shadow: var(--shadow-2xl);
        border-color: var(--primary-light);
    }

    .pet-card-image {
        height: 240px;
        overflow: hidden;
        position: relative;
        background: linear-gradient(45deg, var(--gray-100), var(--gray-50));
    }

    .pet-card-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: var(--transition-slow);
    }

    .pet-card:hover .pet-card-image img {
        transform: scale(1.08);
    }

    .pet-card-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(to bottom, transparent 0%, rgba(0,0,0,0.1) 100%);
        opacity: 0;
        transition: var(--transition-base);
    }

    .pet-card:hover .pet-card-overlay {
        opacity: 1;
    }

    .adoption-badge {
        position: absolute;
        top: var(--spacing-base);
        right: var(--spacing-base);
        background: linear-gradient(135deg, var(--success) 0%, var(--accent2) 100%);
        color: var(--white);
        padding: var(--spacing-xs) var(--spacing-base);
        border-radius: var(--radius-full);
        font-size: var(--font-xs);
        font-weight: var(--fw-semibold);
        box-shadow: var(--shadow-sm);
        z-index: 3;
    }

    .favorite-btn {
        position: absolute;
        top: var(--spacing-base);
        left: var(--spacing-base);
        width: 40px;
        height: 40px;
        border-radius: var(--radius-circle);
        background-color: rgba(255, 255, 255, 0.9);
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition-base);
        z-index: 3;
        backdrop-filter: blur(10px);
    }

    .favorite-btn:hover {
        background-color: var(--white);
        transform: scale(1.1);
    }

    .favorite-btn.favorited {
        background-color: var(--danger);
        color: var(--white);
    }

    .pet-card-content {
        padding: var(--spacing-xl);
    }

    .pet-card-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: var(--spacing-base);
    }

    .pet-card-title {
        font-size: var(--font-xl);
        font-weight: var(--fw-semibold);
        color: var(--text);
        margin: 0;
        line-height: var(--line-height-tight);
    }

    .pet-card-breed {
        color: var(--text-light);
        font-size: var(--font-sm);
        margin-bottom: var(--spacing-base);
        font-weight: var(--fw-medium);
    }

    .pet-card-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: var(--spacing-sm);
        margin-bottom: var(--spacing-xl);
    }

    .pet-card-info-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: var(--font-sm);
        color: var(--text-light);
    }

    .pet-card-info-item i {
        color: var(--primary);
        width: 16px;
        text-align: center;
    }

    .pet-card-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: var(--spacing-base);
        border-top: 1px solid var(--gray-100);
    }

    .pet-card-price {
        font-weight: var(--fw-bold);
        color: var(--primary);
        font-size: var(--font-lg);
    }

    .pet-card-location {
        font-size: var(--font-xs);
        color: var(--text-light);
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: var(--spacing-5xl) var(--spacing-xl);
        background-color: var(--white);
        border-radius: var(--radius-2xl);
        box-shadow: var(--shadow-base);
        margin: var(--spacing-3xl) 0;
    }

    .empty-state-icon {
        font-size: var(--font-4xl);
        color: var(--gray-400);
        margin-bottom: var(--spacing-xl);
    }

    .empty-state h3 {
        color: var(--text);
        margin-bottom: var(--spacing-base);
    }

    .empty-state p {
        color: var(--text-light);
        margin-bottom: var(--spacing-xl);
        max-width: 400px;
        margin-left: auto;
        margin-right: auto;
    }

    /* Enhanced Pagination */
    .pagination-container {
        margin-top: var(--spacing-4xl);
        display: flex;
        justify-content: center;
        align-items: center;
        gap: var(--spacing-xl);
    }

    .pagination {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
        gap: var(--spacing-xs);
        background-color: var(--white);
        border-radius: var(--radius-full);
        padding: var(--spacing-xs);
        box-shadow: var(--shadow-base);
    }

    .page-item a, .page-item span {
        display: flex;
        align-items: center;
        justify-content: center;
        min-width: 44px;
        height: 44px;
        border-radius: var(--radius-full);
        background-color: transparent;
        color: var(--text);
        font-weight: var(--fw-medium);
        transition: var(--transition-base);
        text-decoration: none;
        padding: 0 var(--spacing-sm);
    }

    .page-item a:hover {
        background-color: var(--primary-light);
        color: var(--primary);
        transform: translateY(-2px);
    }

    .page-item.active span {
        background-color: var(--primary);
        color: var(--white);
        box-shadow: var(--shadow-sm);
    }

    .page-item.disabled span {
        color: var(--gray-400);
        cursor: not-allowed;
        opacity: 0.5;
    }

    .pagination-info {
        color: var(--text-light);
        font-size: var(--font-sm);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .pet-hero {
            padding: var(--spacing-3xl) 0 var(--spacing-2xl);
        }

        .pet-hero-title {
            font-size: var(--font-h2);
        }

        .pet-hero-stats {
            flex-direction: column;
            gap: var(--spacing-xl);
        }

        .search-bar {
            flex-direction: column;
            gap: var(--spacing-base);
        }

        .filter-form {
            grid-template-columns: 1fr;
        }

        .filter-actions {
            flex-direction: column;
        }

        .pets-section-header {
            flex-direction: column;
            gap: var(--spacing-base);
            align-items: stretch;
        }

        .pet-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-xl);
        }

        .pet-card-info {
            grid-template-columns: 1fr;
        }

        .pagination {
            gap: var(--spacing-xs);
            padding: var(--spacing-xs);
        }

        .page-item a, .page-item span {
            min-width: 36px;
            height: 36px;
            font-size: var(--font-sm);
        }

        .pagination-container {
            flex-direction: column;
            gap: var(--spacing-base);
        }
    }

    @media (max-width: 480px) {
        .pet-hero-content {
            max-width: 95%;
        }

        .pet-card-content {
            padding: var(--spacing-base);
        }

        .search-input {
            padding: var(--spacing-sm) var(--spacing-base) var(--spacing-sm) var(--spacing-3xl);
        }
    }

    /* Loading States */
    .loading-skeleton {
        background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }

    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    /* Accessibility */
    .sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border: 0;
    }

    /* Focus styles for better accessibility */
    .pet-card:focus-within {
        outline: 2px solid var(--primary);
        outline-offset: 2px;
    }

    .favorite-btn:focus,
    .search-input:focus,
    .sort-select:focus {
        outline: 2px solid var(--primary);
        outline-offset: 2px;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="pet-hero">
    <div class="container">
        <div class="pet-hero-content">
            <h1 class="pet-hero-title">Find Your Perfect Pet</h1>
            <p class="pet-hero-description">
                Browse our selection of adorable pets looking for loving homes. Filter by category, breed, and more to find your perfect companion who will bring joy and love to your family.
            </p>

            <div class="pet-hero-stats">
                <div class="hero-stat">
                    <span class="hero-stat-number">{{ pets.count|default:"500+" }}</span>
                    <span class="hero-stat-label">Pets Available</span>
                </div>
                <div class="hero-stat">
                    <span class="hero-stat-number">1,200+</span>
                    <span class="hero-stat-label">Happy Families</span>
                </div>
                <div class="hero-stat">
                    <span class="hero-stat-number">98%</span>
                    <span class="hero-stat-label">Success Rate</span>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="container">
    <!-- Search and Filter Section -->
    <div class="search-filter-section">
        <!-- Search Bar -->
        <div class="search-bar-container">
            <div class="search-bar">
                <div class="search-input-wrapper">
                    <i class="fas fa-search search-icon"></i>
                    <input type="text" class="search-input" placeholder="Search for pets by name, breed, or location..." id="pet-search">
                </div>
                <div class="view-toggle">
                    <button class="view-toggle-btn active" data-view="grid" title="Grid View">
                        <i class="fas fa-th"></i>
                    </button>
                    <button class="view-toggle-btn" data-view="list" title="List View">
                        <i class="fas fa-list"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Enhanced Filters -->
        <div class="pet-filters">
            <div class="filters-header">
                <h3 class="filters-title">
                    <i class="fas fa-filter"></i>
                    Filter Pets
                </h3>
                <button type="button" class="btn btn-text btn-sm" id="toggle-filters">
                    <i class="fas fa-chevron-up"></i> Hide Filters
                </button>
            </div>

            <form method="get" class="filter-form" id="filter-form">
                <div class="filter-group">
                    <i class="fas fa-paw filter-group-icon"></i>
                    <label for="category" class="form-label">Pet Category</label>
                    <select name="category" id="category" class="form-control">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                            <option value="{{ category.name|lower }}" {% if current_category == category.name|lower %}selected{% endif %}>{{ category.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="filter-group">
                    <i class="fas fa-dog filter-group-icon"></i>
                    <label for="breed" class="form-label">Breed</label>
                    <select name="breed" id="breed" class="form-control">
                        <option value="">All Breeds</option>
                        <!-- Breeds will be populated via AJAX based on selected category -->
                    </select>
                </div>

                <div class="filter-group">
                    <i class="fas fa-heart filter-group-icon"></i>
                    <label for="adoption" class="form-label">Adoption Status</label>
                    <select name="adoption" id="adoption" class="form-control">
                        <option value="">All Pets</option>
                        <option value="yes" {% if request.GET.adoption == 'yes' %}selected{% endif %}>For Adoption</option>
                    </select>
                </div>

                <div class="filter-group">
                    <i class="fas fa-birthday-cake filter-group-icon"></i>
                    <label for="age" class="form-label">Age Range</label>
                    <select name="age" id="age" class="form-control">
                        <option value="">Any Age</option>
                        <option value="puppy">Puppy/Kitten (0-1 year)</option>
                        <option value="young">Young (1-3 years)</option>
                        <option value="adult">Adult (3-7 years)</option>
                        <option value="senior">Senior (7+ years)</option>
                    </select>
                </div>

                <div class="filter-actions">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> Apply Filters
                    </button>
                    <a href="{% url 'pet-list' %}" class="btn btn-outline">
                        <i class="fas fa-times"></i> Clear All
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Pets Section Header -->
    <div class="pets-section-header">
        <div class="pets-count">
            {% if pets %}
                Showing {{ pets|length }} of {{ paginator.count|default:pets|length }} pets
            {% else %}
                No pets found
            {% endif %}
        </div>
        <div class="sort-dropdown">
            <select class="sort-select" id="sort-pets">
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="name">Name A-Z</option>
                <option value="price-low">Price: Low to High</option>
                <option value="price-high">Price: High to Low</option>
            </select>
        </div>
    </div>

    <!-- Enhanced Pet Grid -->
    <div class="pet-grid" id="pet-grid">
        {% for pet in pets %}
            <div class="pet-card" data-pet-id="{{ pet.id }}">
                <div class="pet-card-image">
                    <img src="{{ pet.profile_picture.url }}" alt="{{ pet.name }}" loading="lazy">
                    <div class="pet-card-overlay"></div>

                    <!-- Favorite Button -->
                    <button class="favorite-btn" data-pet-id="{{ pet.id }}" title="Add to favorites">
                        <i class="far fa-heart"></i>
                    </button>

                    <!-- Adoption Badge -->
                    {% if pet.is_for_adoption %}
                        <div class="adoption-badge">
                            <i class="fas fa-heart"></i> For Adoption
                        </div>
                    {% endif %}
                </div>

                <div class="pet-card-content">
                    <div class="pet-card-header">
                        <h3 class="pet-card-title">{{ pet.name }}</h3>
                    </div>

                    <p class="pet-card-breed">{{ pet.breed.name|default:"Mixed Breed" }}</p>

                    <div class="pet-card-info">
                        <div class="pet-card-info-item">
                            <i class="fas fa-venus-mars"></i>
                            <span>{{ pet.get_gender_display }}</span>
                        </div>

                        <div class="pet-card-info-item">
                            <i class="fas fa-birthday-cake"></i>
                            <span>
                                {% if pet.birth_date %}
                                    {{ pet.birth_date|timesince }} old
                                {% else %}
                                    Age unknown
                                {% endif %}
                            </span>
                        </div>

                        <div class="pet-card-info-item">
                            <i class="fas fa-paw"></i>
                            <span>{{ pet.category.name }}</span>
                        </div>

                        <div class="pet-card-info-item">
                            <i class="fas fa-user"></i>
                            <span>{{ pet.owner.get_full_name|default:pet.owner.username }}</span>
                        </div>
                    </div>

                    <div class="pet-card-footer">
                        {% if pet.is_for_adoption and pet.adoption_price %}
                            <div class="pet-card-price">${{ pet.adoption_price }}</div>
                        {% else %}
                            <div class="pet-card-location">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Contact for details</span>
                            </div>
                        {% endif %}

                        <a href="{% url 'pet-detail' pk=pet.pk %}" class="btn btn-primary btn-sm">
                            View Details
                        </a>
                    </div>
                </div>
            </div>
        {% empty %}
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3>No pets found</h3>
                <p>We couldn't find any pets matching your search criteria. Try adjusting your filters or search terms to find more results.</p>
                <div>
                    <a href="{% url 'pet-list' %}" class="btn btn-primary">Clear All Filters</a>
                    <a href="{% url 'home' %}" class="btn btn-outline">Back to Home</a>
                </div>
            </div>
        {% endfor %}
    </div>

    <!-- Enhanced Pagination -->
    {% if is_paginated %}
        <div class="pagination-container">
            <div class="pagination-info">
                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
            </div>

            <ul class="pagination">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="page-link" title="First page">
                            <i class="fas fa-angle-double-left"></i>
                            <span class="sr-only">First page</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="page-link" title="Previous page">
                            <i class="fas fa-angle-left"></i>
                            <span class="sr-only">Previous page</span>
                        </a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="fas fa-angle-double-left"></i>
                            <span class="sr-only">First page (disabled)</span>
                        </span>
                    </li>
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="fas fa-angle-left"></i>
                            <span class="sr-only">Previous page (disabled)</span>
                        </span>
                    </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="page-link">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="page-link" title="Next page">
                            <i class="fas fa-angle-right"></i>
                            <span class="sr-only">Next page</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" class="page-link" title="Last page">
                            <i class="fas fa-angle-double-right"></i>
                            <span class="sr-only">Last page</span>
                        </a>
                    </li>
                {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="fas fa-angle-right"></i>
                            <span class="sr-only">Next page (disabled)</span>
                        </span>
                    </li>
                    <li class="page-item disabled">
                        <span class="page-link">
                            <i class="fas fa-angle-double-right"></i>
                            <span class="sr-only">Last page (disabled)</span>
                        </span>
                    </li>
                {% endif %}
            </ul>

            <div class="pagination-info">
                {{ paginator.count }} total pets
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Enhanced breed loading functionality
        const categorySelect = document.getElementById('category');
        const breedSelect = document.getElementById('breed');

        if (categorySelect && breedSelect) {
            categorySelect.addEventListener('change', function() {
                const category = this.value;

                if (category) {
                    // Show loading state
                    breedSelect.innerHTML = '<option value="">Loading breeds...</option>';
                    breedSelect.disabled = true;

                    // Fetch breeds for selected category
                    fetch(`{% url 'ajax-load-breeds' %}?category=${category}`)
                        .then(response => response.json())
                        .then(data => {
                            // Clear current options
                            breedSelect.innerHTML = '<option value="">All Breeds</option>';

                            // Add new options
                            data.breeds.forEach(breed => {
                                const option = document.createElement('option');
                                option.value = breed.id;
                                option.textContent = breed.name;
                                breedSelect.appendChild(option);
                            });

                            breedSelect.disabled = false;
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            breedSelect.innerHTML = '<option value="">Error loading breeds</option>';
                            breedSelect.disabled = false;
                        });
                } else {
                    // Reset breed select if no category selected
                    breedSelect.innerHTML = '<option value="">All Breeds</option>';
                    breedSelect.disabled = false;
                }
            });

            // Trigger change event on page load if category is selected
            if (categorySelect.value) {
                categorySelect.dispatchEvent(new Event('change'));
            }
        }

        // Search functionality
        const searchInput = document.getElementById('pet-search');
        const petCards = document.querySelectorAll('.pet-card');
        let searchTimeout;

        if (searchInput) {
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                const searchTerm = this.value.toLowerCase().trim();

                searchTimeout = setTimeout(() => {
                    petCards.forEach(card => {
                        const petName = card.querySelector('.pet-card-title').textContent.toLowerCase();
                        const petBreed = card.querySelector('.pet-card-breed').textContent.toLowerCase();
                        const petOwner = card.querySelector('.pet-card-info-item:last-child span').textContent.toLowerCase();

                        const isVisible = petName.includes(searchTerm) ||
                                        petBreed.includes(searchTerm) ||
                                        petOwner.includes(searchTerm);

                        card.style.display = isVisible ? 'block' : 'none';
                    });

                    // Update results count
                    const visibleCards = document.querySelectorAll('.pet-card[style="display: block"], .pet-card:not([style*="display: none"])');
                    const countElement = document.querySelector('.pets-count');
                    if (countElement) {
                        countElement.textContent = `Showing ${visibleCards.length} pets`;
                    }
                }, 300);
            });
        }

        // View toggle functionality
        const viewToggleBtns = document.querySelectorAll('.view-toggle-btn');
        const petGrid = document.getElementById('pet-grid');

        viewToggleBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const view = this.dataset.view;

                // Update active state
                viewToggleBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Update grid layout
                if (view === 'list') {
                    petGrid.style.gridTemplateColumns = '1fr';
                    petGrid.style.gap = 'var(--spacing-base)';
                } else {
                    petGrid.style.gridTemplateColumns = 'repeat(auto-fill, minmax(320px, 1fr))';
                    petGrid.style.gap = 'var(--spacing-2xl)';
                }
            });
        });

        // Favorite functionality
        const favoriteButtons = document.querySelectorAll('.favorite-btn');

        favoriteButtons.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const petId = this.dataset.petId;
                const icon = this.querySelector('i');

                // Toggle favorite state
                if (this.classList.contains('favorited')) {
                    this.classList.remove('favorited');
                    icon.className = 'far fa-heart';
                    this.title = 'Add to favorites';
                } else {
                    this.classList.add('favorited');
                    icon.className = 'fas fa-heart';
                    this.title = 'Remove from favorites';

                    // Add animation
                    this.style.transform = 'scale(1.2)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 200);
                }

                // Here you would typically send an AJAX request to save the favorite state
                // fetch('/pets/favorite/', { method: 'POST', body: JSON.stringify({pet_id: petId}) })
            });
        });

        // Filter toggle functionality
        const toggleFiltersBtn = document.getElementById('toggle-filters');
        const filterForm = document.getElementById('filter-form');

        if (toggleFiltersBtn && filterForm) {
            toggleFiltersBtn.addEventListener('click', function() {
                const isHidden = filterForm.style.display === 'none';
                const icon = this.querySelector('i');

                if (isHidden) {
                    filterForm.style.display = 'grid';
                    icon.className = 'fas fa-chevron-up';
                    this.innerHTML = '<i class="fas fa-chevron-up"></i> Hide Filters';
                } else {
                    filterForm.style.display = 'none';
                    icon.className = 'fas fa-chevron-down';
                    this.innerHTML = '<i class="fas fa-chevron-down"></i> Show Filters';
                }
            });
        }

        // Sort functionality
        const sortSelect = document.getElementById('sort-pets');

        if (sortSelect) {
            sortSelect.addEventListener('change', function() {
                const sortValue = this.value;
                const petCardsArray = Array.from(petCards);

                petCardsArray.sort((a, b) => {
                    switch (sortValue) {
                        case 'name':
                            const nameA = a.querySelector('.pet-card-title').textContent;
                            const nameB = b.querySelector('.pet-card-title').textContent;
                            return nameA.localeCompare(nameB);

                        case 'price-low':
                        case 'price-high':
                            const priceA = parseFloat(a.querySelector('.pet-card-price')?.textContent.replace('$', '') || '0');
                            const priceB = parseFloat(b.querySelector('.pet-card-price')?.textContent.replace('$', '') || '0');
                            return sortValue === 'price-low' ? priceA - priceB : priceB - priceA;

                        case 'oldest':
                            return parseInt(a.dataset.petId) - parseInt(b.dataset.petId);

                        case 'newest':
                        default:
                            return parseInt(b.dataset.petId) - parseInt(a.dataset.petId);
                    }
                });

                // Re-append sorted cards
                petCardsArray.forEach(card => petGrid.appendChild(card));
            });
        }

        // Add smooth scrolling to pagination links
        const paginationLinks = document.querySelectorAll('.pagination a');
        paginationLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                // Add loading state
                this.style.opacity = '0.6';
                this.style.pointerEvents = 'none';
            });
        });

        // Lazy loading for images
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src || img.src;
                        img.classList.remove('loading-skeleton');
                        observer.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('.pet-card-image img').forEach(img => {
                img.classList.add('loading-skeleton');
                imageObserver.observe(img);
            });
        }
    });
</script>
{% endblock %}
