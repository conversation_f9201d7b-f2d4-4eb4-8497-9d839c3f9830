from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from users.models import UserActivity, UserRole, Wishlist
from users.utils import create_user_activity, add_to_wishlist, assign_user_role
from pets.models import Pet
from shop.models import Product
from services.models import Service

User = get_user_model()


class Command(BaseCommand):
    help = 'Create sample user activities and wishlist items for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--users',
            type=int,
            default=5,
            help='Number of users to create activities for'
        )

    def handle(self, *args, **options):
        users_count = options['users']
        
        # Get or create some users
        users = []
        for i in range(users_count):
            username = f'testuser{i+1}'
            user, created = User.objects.get_or_create(
                username=username,
                defaults={
                    'email': f'{username}@example.com',
                    'first_name': f'Test',
                    'last_name': f'User {i+1}',
                    'bio': f'This is test user {i+1} for demonstrating the enhanced profile features.',
                }
            )
            users.append(user)
            
            if created:
                self.stdout.write(f'Created user: {username}')
            
            # Assign roles to users
            if i == 0:
                assign_user_role(user, 'pet_owner', is_primary=True)
                assign_user_role(user, 'service_provider')
            elif i == 1:
                assign_user_role(user, 'service_provider', is_primary=True)
            elif i == 2:
                assign_user_role(user, 'shop_vendor', is_primary=True)
            else:
                assign_user_role(user, 'regular', is_primary=True)
        
        # Create sample activities
        self.create_sample_activities(users)
        
        # Create sample wishlist items
        self.create_sample_wishlist_items(users)
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created sample data for {users_count} users'
            )
        )

    def create_sample_activities(self, users):
        """Create sample activities for users"""
        activity_templates = [
            ('pet_added', 'Added a new pet to their profile'),
            ('pet_updated', 'Updated their pet\'s information'),
            ('review_written', 'Wrote a review for a service'),
            ('service_booked', 'Booked a pet grooming service'),
            ('order_placed', 'Placed an order for pet supplies'),
            ('profile_updated', 'Updated their profile information'),
            ('became_provider', 'Became a verified service provider'),
            ('service_added', 'Added a new service offering'),
            ('pet_followed', 'Started following a cute pet'),
            ('user_followed', 'Started following another pet lover'),
        ]
        
        for user in users:
            # Create 3-7 activities per user
            import random
            num_activities = random.randint(3, 7)
            
            for _ in range(num_activities):
                activity_type, description = random.choice(activity_templates)
                
                # Get a related object if available
                content_object = None
                if activity_type in ['pet_added', 'pet_updated', 'pet_followed']:
                    pets = Pet.objects.all()
                    if pets.exists():
                        content_object = random.choice(pets)
                elif activity_type in ['order_placed']:
                    products = Product.objects.all()
                    if products.exists():
                        content_object = random.choice(products)
                elif activity_type in ['service_booked', 'service_added']:
                    services = Service.objects.all()
                    if services.exists():
                        content_object = random.choice(services)
                
                create_user_activity(
                    user=user,
                    activity_type=activity_type,
                    description=description,
                    content_object=content_object,
                    is_public=random.choice([True, True, True, False])  # 75% public
                )
        
        self.stdout.write('Created sample activities')

    def create_sample_wishlist_items(self, users):
        """Create sample wishlist items for users"""
        for user in users:
            # Add some pets to wishlist
            pets = Pet.objects.all()[:3]
            for pet in pets:
                if random.choice([True, False]):  # 50% chance
                    add_to_wishlist(
                        user=user,
                        item=pet,
                        item_type='pet',
                        notes=f'Interested in adopting {pet.name}'
                    )
            
            # Add some products to wishlist
            products = Product.objects.all()[:3]
            for product in products:
                if random.choice([True, False]):  # 50% chance
                    add_to_wishlist(
                        user=user,
                        item=product,
                        item_type='product',
                        notes=f'Want to buy this for my pet'
                    )
            
            # Add some services to wishlist
            services = Service.objects.all()[:2]
            for service in services:
                if random.choice([True, False]):  # 50% chance
                    add_to_wishlist(
                        user=user,
                        item=service,
                        item_type='service',
                        notes=f'Planning to book this service'
                    )
        
        self.stdout.write('Created sample wishlist items')
