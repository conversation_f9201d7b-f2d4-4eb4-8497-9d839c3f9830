from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.views.generic import DetailView, UpdateView
from django.urls import reverse_lazy
from .models import User, UserProfile, Address
from .forms import UserProfileForm, ExtendedProfileForm, AddressForm


class UserProfileView(DetailView):
    """View for displaying user profile"""
    model = User
    template_name = 'users/profile.html'
    context_object_name = 'profile_user'

    def get_object(self):
        return get_object_or_404(User, username=self.kwargs.get('username'))

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user = self.get_object()

        # Get user's pets
        context['pets'] = user.pets.all()

        # Check if current user is following the profile user
        context['is_following'] = False
        if self.request.user.is_authenticated:
            if user.profile.followers.filter(id=self.request.user.id).exists():
                context['is_following'] = True

        return context


@login_required
def edit_profile(request):
    """View for editing user profile"""
    if request.method == 'POST':
        user_form = UserProfileForm(request.POST, request.FILES, instance=request.user)
        profile_form = ExtendedProfileForm(request.POST, instance=request.user.profile)

        if user_form.is_valid() and profile_form.is_valid():
            user_form.save()
            profile_form.save()
            messages.success(request, 'Your profile has been updated successfully!')
            return redirect('user-profile', username=request.user.username)
    else:
        user_form = UserProfileForm(instance=request.user)
        profile_form = ExtendedProfileForm(instance=request.user.profile)

    return render(request, 'users/edit_profile.html', {
        'user_form': user_form,
        'profile_form': profile_form
    })


@login_required
def follow_user(request, username):
    """View for following/unfollowing users"""
    user_to_follow = get_object_or_404(User, username=username)

    if request.user == user_to_follow:
        messages.error(request, "You cannot follow yourself.")
        return redirect('user-profile', username=username)

    if user_to_follow.profile.followers.filter(id=request.user.id).exists():
        user_to_follow.profile.followers.remove(request.user)
        messages.success(request, f"You have unfollowed {username}.")
    else:
        user_to_follow.profile.followers.add(request.user)
        messages.success(request, f"You are now following {username}.")

    return redirect('user-profile', username=username)


@login_required
def address_list(request):
    """View for listing user addresses"""
    addresses = Address.objects.filter(user=request.user)
    return render(request, 'users/address_list.html', {'addresses': addresses})


@login_required
def add_address(request):
    """View for adding a new address"""
    if request.method == 'POST':
        form = AddressForm(request.POST)
        if form.is_valid():
            address = form.save(commit=False)
            address.user = request.user

            # If this address is set as default, unset any other default of same type
            if address.default:
                Address.objects.filter(
                    user=request.user,
                    address_type=address.address_type,
                    default=True
                ).update(default=False)

            address.save()
            messages.success(request, 'Address added successfully!')
            return redirect('address-list')
    else:
        form = AddressForm()

    return render(request, 'users/address_form.html', {'form': form, 'title': 'Add Address'})
