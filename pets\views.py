from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib import messages
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy, reverse
from django.http import JsonResponse
from .models import Pet, PetCategory, PetBreed, PetGallery, PetMedicalRecord
from .forms import PetForm, PetGalleryForm, PetMedicalRecordForm


class PetListView(ListView):
    """View for listing all pets"""
    model = Pet
    template_name = 'pets/pet_list.html'
    context_object_name = 'pets'
    paginate_by = 12

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by category if provided
        category = self.request.GET.get('category')
        if category:
            queryset = queryset.filter(category__name__iexact=category)

        # Filter by adoption status if provided
        adoption = self.request.GET.get('adoption')
        if adoption == 'yes':
            queryset = queryset.filter(is_for_adoption=True)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = PetCategory.objects.all()
        return context


class PetDetailView(DetailView):
    """View for displaying pet details"""
    model = Pet
    template_name = 'pets/pet_detail.html'
    context_object_name = 'pet'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['gallery'] = self.object.gallery.all()

        # Check if user is following this pet
        if self.request.user.is_authenticated:
            context['is_following'] = self.object.followers.filter(id=self.request.user.id).exists()

            # Check if pet is in user's wishlist
            from users.utils import is_in_wishlist
            context['in_wishlist'] = is_in_wishlist(self.request.user, self.object)

        return context


class PetCreateView(LoginRequiredMixin, CreateView):
    """View for creating a new pet"""
    model = Pet
    form_class = PetForm
    template_name = 'pets/pet_form.html'

    def form_valid(self, form):
        form.instance.owner = self.request.user
        messages.success(self.request, 'Pet profile created successfully!')
        return super().form_valid(form)


class PetUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """View for updating a pet"""
    model = Pet
    form_class = PetForm
    template_name = 'pets/pet_form.html'

    def form_valid(self, form):
        messages.success(self.request, 'Pet profile updated successfully!')
        return super().form_valid(form)

    def test_func(self):
        pet = self.get_object()
        return self.request.user == pet.owner


class PetDeleteView(LoginRequiredMixin, UserPassesTestMixin, DeleteView):
    """View for deleting a pet"""
    model = Pet
    template_name = 'pets/pet_confirm_delete.html'
    success_url = reverse_lazy('pet-list')

    def test_func(self):
        pet = self.get_object()
        return self.request.user == pet.owner

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Pet profile deleted successfully!')
        return super().delete(request, *args, **kwargs)


@login_required
def follow_pet(request, pk):
    """View for following/unfollowing a pet"""
    pet = get_object_or_404(Pet, pk=pk)

    if request.user == pet.owner:
        messages.error(request, "You cannot follow your own pet.")
        return redirect('pet-detail', pk=pk)

    if pet.followers.filter(id=request.user.id).exists():
        pet.followers.remove(request.user)
        messages.success(request, f"You have unfollowed {pet.name}.")
    else:
        pet.followers.add(request.user)
        messages.success(request, f"You are now following {pet.name}.")

    return redirect('pet-detail', pk=pk)


@login_required
def add_pet_photo(request, pk):
    """View for adding photos to pet gallery"""
    pet = get_object_or_404(Pet, pk=pk)

    if request.user != pet.owner:
        messages.error(request, "You can only add photos to your own pets.")
        return redirect('pet-detail', pk=pk)

    if request.method == 'POST':
        form = PetGalleryForm(request.POST, request.FILES)
        if form.is_valid():
            photo = form.save(commit=False)
            photo.pet = pet
            photo.save()
            messages.success(request, 'Photo added successfully!')
            return redirect('pet-detail', pk=pk)
    else:
        form = PetGalleryForm()

    return render(request, 'pets/add_pet_photo.html', {'form': form, 'pet': pet})


@login_required
def add_medical_record(request, pk):
    """View for adding medical records to a pet"""
    pet = get_object_or_404(Pet, pk=pk)

    if request.user != pet.owner:
        messages.error(request, "You can only add medical records to your own pets.")
        return redirect('pet-detail', pk=pk)

    if request.method == 'POST':
        form = PetMedicalRecordForm(request.POST, request.FILES)
        if form.is_valid():
            record = form.save(commit=False)
            record.pet = pet
            record.save()
            messages.success(request, 'Medical record added successfully!')
            return redirect('pet-detail', pk=pk)
    else:
        form = PetMedicalRecordForm()

    return render(request, 'pets/add_medical_record.html', {'form': form, 'pet': pet})


def load_breeds(request):
    """AJAX view for loading breeds based on selected category"""
    category_id = request.GET.get('category')
    breeds = PetBreed.objects.filter(category_id=category_id).order_by('name')
    return JsonResponse({'breeds': list(breeds.values('id', 'name'))}, safe=False)
